<template>
  <div class="complete-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "completeChart",
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
            show: false
        },
       grid: {
         left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.5)",
            },
          },
          axisTick: {
            show: true,
          },
          axisLabel: {
            color: "#ACC2E2",
          },
          splitLine: {
            lineStyle: {
              color: "rgba(172, 194, 226, 0.2)",
            },
          },
        },
        yAxis: {
          type: "category",
          data: ["陵水17-2", "崖城13-1", "陵水25-1", "文昌16-2", "崖城13-10"],
          axisLabel: {
            color: "#ACC2E2",
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(172, 194, 226, 0.5)",
            },
          },
          axisTick: {
            show: true,
          },
          boundaryGap: true,
        },
        series: [
          {
            name: "2011",
            type: "bar",
            data: [10, 45, 23, 88, 62],
            barCategoryGap: "60%",
          },
        ],
      };
      mychart.setOption(option);
    },
  },
};
</script>
<style lang="scss" scoped>
.complete-chart {
  width: 100%;
  .chart-box {
    width: 100%;
    height: 280px;
  }
}
</style>
