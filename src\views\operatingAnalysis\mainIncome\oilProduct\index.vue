<template>
  <div class="oilProduct">
    <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <CarouselBtn :buttons="buttons" />
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </chartBox>
      </div>
      <div class="statistics-box">
        <chartBox :title="'分油气田统计'">
          <div class="table-box">
            <CommonTable
              :tableData="tableData"
              :colums="colums"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="comparison-box">
        <chartBox :title="'分油气田对比'">
          <div class="medice-radio">
            <el-radio
              v-for="item in medicine"
              :key="item.value"
              v-model="radio"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </div>
          <ComparisonChart />
        </chartBox>
      </div>
      <div class="completion-rate">
        <chartBox :title="'分油气田预算完成率'">
          <div class="medice-radio">
            <el-radio
              v-for="item in medicine"
              :key="item.value"
              v-model="radio"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </div>
          <CompletChart />
        </chartBox>
      </div>
      <div class="trend-box">
        <chartBox :title="'趋势变动'">
          <div><CarouselBtn :buttons="buttons" /></div>
          <div class="medice-radio">
            <el-radio
              v-for="item in medicine"
              :key="item.value"
              v-model="radio"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </div>
          <TrendChart />
        </chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import ItemCard from "../../components/ItemCard.vue";
import CommonTable from "@/components/comTable/commonTable.vue";
import ComparisonChart from "./comparisonChart/index.vue";
import CompletChart from "./completChart/index.vue";
import TrendChart from "./trendChart/index.vue";
import CarouselBtn from "../../components/CarouselBtn.vue";
export default {
  name: "oilProduct",
  components: {
    ItemCard,
    ComparisonChart,
    CompletChart,
    CommonTable,
    TrendChart,
    CarouselBtn,
  },
  data() {
    return {
      medicine: [
        {
          value: "all",
          label: "油气合计",
        },
        {
          value: "gas",
          label: "天然气",
        },
        {
          value: "oil",
          label: "凝析油",
        },
        {
          value: "condensate",
          label: "原油",
        },
      ],
      radio: "all",
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      cardData: [
        { title: "油气总产量", value: "10000" },
        { title: "天然气总产量", value: "10000" },
        { title: "石油液体总产量", value: "10000" },
      ],
      colums: [
        {
          label: "指标",
          prop: "indicator",
        },
        {
          label: "本年累计",
          prop: "currentYear",
        },
        {
          label: "同期挑战目标",
          prop: "challengeTarget",
        },
        {
          label: "同期目标完成率",
          prop: "completionRate",
        },
        {
          label: "全年挑战目标",
          prop: "yearChallengeTarget",
        },
        {
          label: "全年目标完成率",
          prop: "yearCompletionRate",
        },
      ],
      tableData: [
        {
          indicator: "油气总产量",
          currentYear: "10000",
          challengeTarget: "10000",
          completionRate: "100%",
          yearChallengeTarget: "10000",
          yearCompletionRate: "100%",
        },
        {
          indicator: "天然气总产量",
          currentYear: "10000",
          challengeTarget: "10000",
          completionRate: "100%",
          yearChallengeTarget: "10000",
          yearCompletionRate: "100%",
        },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.oilProduct {
  height: calc(100vh - 84px); // 减去Header高度
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .content-up {
    display: flex;
    justify-content: space-between;
    height: 45%; // 上半部分占45%高度
    min-height: 300px; // 最小高度保障

    .main-indicators {
      flex: 1;
      min-width: 0;
      margin-right: 10px;
      height: 100%;
    }
    .statistics-box {
      flex: 1;
      min-width: 0;
      height: 100%;

      .table-box {
        margin: 16px;
      }
    }
  }

  .card-box {
    display: flex;
    justify-content: space-between;
    margin: 14px 44px;
    .item-card {
      flex: 1;
      margin-right: 16px;
    }
  }

  .content-down {
    display: flex;
    gap: 10px; // 所有容器之间统一间隔
    margin-top: 10px;
    flex: 1; // 下半部分占剩余空间
    min-height: 0; // 允许flex收缩

    .comparison-box,
    .completion-rate,
    .trend-box {
      flex: 1;
      min-width: 0;
      height: 100%; // 确保子容器充满高度
      display: flex;
      flex-direction: column;

      .medice-radio {
        margin-top: 16px;
        padding-left: 16px;
        flex-shrink: 0; // 防止单选按钮被压缩

        .el-radio {
          margin-right: 16px;
        }
      }
    }
  }
}

::v-deep .el-radio__inner {
  border: 1px solid #1783ff;
  background: rgba(23, 131, 255, 0.3);
}

::v-deep .el-radio__label {
  color: #acc2e2;
}
</style>
